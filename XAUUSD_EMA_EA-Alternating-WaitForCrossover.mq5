//+------------------------------------------------------------------+
//|                XAUUSD_EMA_EA-Alternating-WaitForCrossover.mq5      |
//|                                                                    |
//|     EMA-based strategy for Gold trading on 1-minute chart          |
//|     Auto-detects symbol (works with XAUUSD, GOLD, etc.)            |
//|     Uses EMA crossover as a trigger to open trades                 |
//|     Alternates between BUY and SELL trades in sequence             |
//|     First trade is BUY, then after SL/TP hit, waits for            |
//|     EMA crossover and opens SELL, then BUY, and so on              |
//|     ALWAYS waits for a new EMA crossover after TP/SL is hit        |
//|     Only allows one position at a time (for effective martingale)   |
//|     Enhanced spread handling for multi-broker compatibility         |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025"
#property link      ""
#property version   "1.00"
#property strict

// Include necessary libraries
#include <Trade\Trade.mqh>

// Global variables
CTrade trade;                   // Trade object
int g_ema_handle;              // EMA indicator handle
int g_total_positions = 0;     // Current number of open positions
datetime g_last_trade_time = 0; // Time of the last trade
datetime g_ea_start_time = 0;   // Time when the EA was started
bool g_initialization_complete = false; // Flag to indicate if initialization is complete
bool g_waiting_for_crossover = false;  // Flag to ensure we wait for a proper crossover after TP/SL
string g_symbol = "";          // Current chart symbol (auto-detected)

// Spread tracking variables
double g_current_spread = 0.0;  // Current spread in points
double g_avg_spread = 0.0;      // Average spread in points
double g_max_spread = 0.0;      // Maximum spread observed
double g_min_spread = 0.0;      // Minimum spread observed
double g_spread_sum = 0.0;      // Sum of spreads for average calculation
int g_spread_count = 0;         // Count of spread measurements
double g_spread_history[20];    // Array to store recent spread history
datetime g_last_spread_update = 0; // Time of last spread update

// Arrays to track closed positions
ulong g_last_closed_tickets[200]; // Array to store last closed ticket numbers (increased size)
int g_last_closed_count = 0;    // Count of closed positions in the current cycle
datetime g_last_array_cleanup = 0; // Time of last array cleanup

// Arrays to track both position IDs and deal tickets
ulong g_last_closed_positions[200]; // Array to store position IDs
ulong g_last_closed_deals[200];     // Array to store deal tickets
int g_last_closed_position_count = 0; // Count of closed positions

// Martingale variables
double g_current_lot_size = 0;  // Current lot size (will be initialized in OnInit)
int g_consecutive_losses = 0;   // Count of consecutive losses
double g_total_loss_amount = 0; // Total accumulated loss amount for martingale recovery
datetime g_last_martingale_update = 0; // Time of last martingale update

// Alternating trade direction variables
bool g_next_trade_is_buy = true; // Flag to track the next trade direction (true = BUY, false = SELL)
int g_trade_count = 0;          // Total trade count

// Input parameters
// General settings
input int    Magic_Number = 123456;    // Magic number for trade identification
input double Fixed_Lot_Size = 1.00;    // Initial lot size for trades

// Strategy parameters
input int    EMA_Period = 7;           // EMA period for entry signal
input double TP_Pips = 14.0;           // Take profit in pips
input double SL_Pips = 7.0;            // Stop loss in pips
input int    Initialization_Delay = 15; // Delay before EA starts trading (seconds)
input int    Min_Trade_Interval = 60;  // Minimum time between trades (seconds)
input double Pip_Value = 0.1;          // Pip value for Gold (set according to broker)

// Spread handling parameters
input double Max_Allowed_Spread = 30.0; // Maximum allowed spread in points
input bool   Adjust_For_Spread = true;  // Adjust TP/SL for current spread
input double Spread_Buffer = 1.5;       // Buffer multiplier for spread compensation (1.0 = exact spread)
input bool   Use_Dynamic_Spread_Filter = true; // Use dynamic spread filtering based on average spread
input int    Spread_History_Length = 50; // Number of ticks to calculate average spread
input double Max_Spread_Deviation = 2.0; // Maximum allowed deviation from average spread (multiplier)

// Martingale parameters
input bool   Use_Martingale = true;    // Use martingale strategy to recover losses
input double Martingale_Multiplier = 2.0; // Multiplier for lot size after loss
input int    Max_Martingale_Level = 5; // Maximum martingale level (to limit risk)
input double Max_Lot_Size = 10.0;     // Maximum allowed lot size
input bool   Include_Spread_In_Martingale = true; // Include spread cost in martingale calculations

// No longer using a hardcoded symbol - using auto-detection instead

//+------------------------------------------------------------------+
//| Expert initialization function                                    |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize trade object with magic number
   trade.SetExpertMagicNumber(Magic_Number);

   // Auto-detect the current chart symbol
   g_symbol = Symbol();

   // Validate that we're on a gold instrument (XAUUSD, GOLD, etc.)
   if(!IsGoldSymbol(g_symbol))
   {
      Print("WARNING: This EA is designed for Gold trading. Current symbol: ", g_symbol);
      Print("The EA will continue, but please verify this is a Gold instrument.");
   }

   Print("Auto-detected symbol: ", g_symbol);

   // Validate input parameters
   if(!ValidateInputParameters())
      return INIT_PARAMETERS_INCORRECT;

   // Check for valid timeframe (M1)
   if(Period() != PERIOD_M1)
   {
      Print("This EA is designed for M1 timeframe only!");
      return INIT_FAILED;
   }

   // Create EMA indicator handle for the current symbol
   g_ema_handle = iMA(g_symbol, PERIOD_M1, EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   if(g_ema_handle == INVALID_HANDLE)
   {
      Print("Failed to create EMA indicator handle. Error: ", GetLastError());
      return INIT_FAILED;
   }

   // Set timer for regular updates (1 second)
   EventSetTimer(1);

   // Record the EA start time
   g_ea_start_time = TimeCurrent();
   g_last_trade_time = g_ea_start_time;

   // Clear any previously tracked closed positions
   g_last_closed_count = 0;
   g_last_closed_position_count = 0;
   ArrayInitialize(g_last_closed_tickets, 0);
   ArrayInitialize(g_last_closed_positions, 0);
   ArrayInitialize(g_last_closed_deals, 0);
   g_last_array_cleanup = TimeCurrent();
   g_last_martingale_update = TimeCurrent();

   // Initialize martingale variables
   g_current_lot_size = Fixed_Lot_Size;
   g_consecutive_losses = 0;
   g_total_loss_amount = 0;

   // Initialize alternating trade direction variables
   g_next_trade_is_buy = true; // First trade will be BUY
   g_trade_count = 0;

   // Initialize crossover waiting flag
   g_waiting_for_crossover = false;

   // Initialize spread tracking variables
   g_current_spread = 0.0;
   g_avg_spread = 0.0;
   g_max_spread = 0.0;
   g_min_spread = 0.0;
   g_spread_sum = 0.0;
   g_spread_count = 0;
   ArrayInitialize(g_spread_history, 0);
   g_last_spread_update = TimeCurrent();

   // Get initial spread measurement
   UpdateSpreadStats();

   Print("EMA EA initialized successfully. Trading on ", g_symbol, " 1-minute chart with ALTERNATING BUY/SELL SEQUENCE.");
   Print("Entry rules: First trade will be BUY, then after SL/TP hit, wait for EMA crossover and open SELL, then BUY, and so on.");
   Print("IMPORTANT: This EA ALWAYS waits for a new EMA crossover after TP/SL is hit before opening a new trade.");

   if(Use_Martingale)
      Print("Martingale strategy enabled. Initial lot size: ", DoubleToString(g_current_lot_size, 2),
            ", Multiplier: ", DoubleToString(Martingale_Multiplier, 2),
            ", Include spread in calculations: ", (Include_Spread_In_Martingale ? "Yes" : "No"));

   // Log spread handling settings
   Print("Spread handling settings - Max allowed: ", DoubleToString(Max_Allowed_Spread, 1),
         ", Adjust TP/SL for spread: ", (Adjust_For_Spread ? "Yes" : "No"),
         ", Spread buffer: ", DoubleToString(Spread_Buffer, 1),
         ", Dynamic filtering: ", (Use_Dynamic_Spread_Filter ? "Yes" : "No"));

   Print("EA will start trading after initialization delay of ", Initialization_Delay, " seconds.");

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                  |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up objects and timer
   EventKillTimer();

   // Release indicator handles
   if(g_ema_handle != INVALID_HANDLE)
      IndicatorRelease(g_ema_handle);

   Print("EMA EA deinitialized for ", g_symbol);
}

//+------------------------------------------------------------------+
//| Expert tick function                                              |
//+------------------------------------------------------------------+
void OnTick()
{
   // Verify martingale state consistency at the beginning of each tick
   VerifyMartingaleState();

   // Update spread statistics on every tick
   UpdateSpreadStats();

   // Periodically log spread information (every 100 ticks)
   static int spread_log_counter = 0;
   spread_log_counter++;
   if(spread_log_counter >= 100)
   {
      spread_log_counter = 0;
      Print("Spread Statistics - Current: ", DoubleToString(g_current_spread, 1),
            ", Average: ", DoubleToString(g_avg_spread, 1),
            ", Min: ", DoubleToString(g_min_spread, 1),
            ", Max: ", DoubleToString(g_max_spread, 1),
            ", Count: ", g_spread_count);
   }

   // Check if we need to complete initialization
   if(!g_initialization_complete)
   {
      // Wait for Initialization_Delay seconds before allowing trades
      if(TimeCurrent() - g_ea_start_time >= Initialization_Delay)
      {
         g_initialization_complete = true;
         Print("EA initialization delay complete. Trading now enabled.");
         Print("Initial spread measurement: ", DoubleToString(g_current_spread, 1), " points");
      }
      else
      {
         // Still in initialization period, don't trade yet
         return;
      }
   }

   // Update the count of current positions
   g_total_positions = CountOpenPositions();

   // Check for closed positions and handle based on closure reason
   CheckClosedPositions();

   // Check if we already have an open position
   static bool position_logged = false;

   if(g_total_positions > 0)
   {
      // Only log this message once when we have a position, not on every tick
      if(!position_logged)
      {
         Print("Already have an open position. No new trades will be opened until current position is closed.");
         position_logged = true;
      }
      return;
   }
   else
   {
      // Reset the log flag when we have no positions
      position_logged = false;
   }

   // Check for entry signals on the current symbol
   CheckEntrySignal();
}

//+------------------------------------------------------------------+
//| Timer function                                                    |
//+------------------------------------------------------------------+
void OnTimer()
{
   // Skip processing during initialization period
   if(!g_initialization_complete)
      return;

   // Update the count of current positions
   g_total_positions = CountOpenPositions();

   // Check for closed positions
   CheckClosedPositions();
}

//+------------------------------------------------------------------+
//| Check for closed positions and handle based on closure reason     |
//| - If take profit was hit: Set flag to wait for new EMA crossover  |
//| - If stop loss was hit: Set flag to wait for new EMA crossover    |
//+------------------------------------------------------------------+
void CheckClosedPositions()
{
   // Skip if we're still in initialization
   if(!g_initialization_complete)
      return;

   // Periodically clean up old tickets and position/deal arrays (every 4 hours)
   datetime current_time = TimeCurrent();
   if(current_time - g_last_array_cleanup > 4*60*60) // 4 hours in seconds
   {
      // Clean up ticket array
      if(g_last_closed_count > 50)
      {
         Print("Performing scheduled cleanup of processed tickets array. Current count: ", g_last_closed_count);
         // Keep only the most recent 20 tickets
         if(g_last_closed_count > 20)
         {
            // Create a temporary array to hold the most recent tickets
            ulong temp_tickets[20];
            int start_idx = g_last_closed_count - 20;

            // Copy the most recent 20 tickets
            for(int i = 0; i < 20; i++)
            {
               temp_tickets[i] = g_last_closed_tickets[start_idx + i];
            }

            // Reset the main array
            ArrayInitialize(g_last_closed_tickets, 0);

            // Copy back the most recent tickets
            for(int i = 0; i < 20; i++)
            {
               g_last_closed_tickets[i] = temp_tickets[i];
            }

            g_last_closed_count = 20;
            Print("Cleaned up processed tickets array. New count: ", g_last_closed_count);
         }
      }

      // Clean up position/deal arrays
      if(g_last_closed_position_count > 50)
      {
         Print("Performing scheduled cleanup of position/deal arrays. Current count: ", g_last_closed_position_count);
         // Keep only the most recent 20 entries
         if(g_last_closed_position_count > 20)
         {
            // Create temporary arrays to hold the most recent entries
            ulong temp_positions[20];
            ulong temp_deals[20];
            int start_idx = g_last_closed_position_count - 20;

            // Copy the most recent 20 entries
            for(int i = 0; i < 20; i++)
            {
               temp_positions[i] = g_last_closed_positions[start_idx + i];
               temp_deals[i] = g_last_closed_deals[start_idx + i];
            }

            // Reset the main arrays
            ArrayInitialize(g_last_closed_positions, 0);
            ArrayInitialize(g_last_closed_deals, 0);

            // Copy back the most recent entries
            for(int i = 0; i < 20; i++)
            {
               g_last_closed_positions[i] = temp_positions[i];
               g_last_closed_deals[i] = temp_deals[i];
            }

            g_last_closed_position_count = 20;
            Print("Cleaned up position/deal arrays. New count: ", g_last_closed_position_count);
         }
      }

      g_last_array_cleanup = current_time;
   }

   // Get history deals only since EA was started (not the full day)
   // This prevents processing old closed positions on startup
   HistorySelect(g_ea_start_time, TimeCurrent());
   int total_deals = HistoryDealsTotal();

   // Loop through recent deals to find closed positions
   for(int i = 0; i < total_deals; i++)
   {
      ulong deal_ticket = HistoryDealGetTicket(i);

      // Skip if we can't select the deal
      if(deal_ticket <= 0) continue;

      // Check if this is a position close deal with our magic number
      if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) == Magic_Number &&
         HistoryDealGetInteger(deal_ticket, DEAL_ENTRY) == DEAL_ENTRY_OUT)
      {
         // Get the position ticket that was closed
         ulong position_ticket = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);

         // Skip if we've already processed this position or deal
         if(IsPositionInArray(position_ticket) || IsDealInArray(deal_ticket))
         {
            // Reduce log output by not printing this message every time
            // Print("Position #", position_ticket, " or Deal #", deal_ticket, " already processed. Skipping.");
            continue;
         }

         Print("Processing new closed position #", position_ticket, " with deal #", deal_ticket);

         // Add to our processed arrays
         AddPositionAndDealToArrays(position_ticket, deal_ticket);
         AddTicketToArray(position_ticket); // Keep for backward compatibility

         // Get the symbol of the closed position
         string symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);

         // Skip if not our target symbol
         if(symbol != g_symbol)
            continue;

         // Get the position type (buy or sell)
         long deal_type = HistoryDealGetInteger(deal_ticket, DEAL_TYPE);

         // In MT5, when a position is closed:
         // - If it was a BUY position, the closing deal will be of type DEAL_TYPE_SELL
         // - If it was a SELL position, the closing deal will be of type DEAL_TYPE_BUY
         bool was_buy = (deal_type == DEAL_TYPE_SELL);

         // Get the deal profit and reason
         double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
         ENUM_DEAL_REASON deal_reason = (ENUM_DEAL_REASON)HistoryDealGetInteger(deal_ticket, DEAL_REASON);

         // Log the position closure details
         Print("Closed position details - Deal Type: ", EnumToString((ENUM_DEAL_TYPE)deal_type),
               ", Was Buy: ", was_buy,
               ", Profit: ", DoubleToString(deal_profit, 2),
               ", Reason: ", EnumToString(deal_reason));

         // Check if the position was closed with profit AND by take profit
         bool was_tp_hit = (deal_reason == DEAL_REASON_TP);
         bool was_sl_hit = (deal_reason == DEAL_REASON_SL);

         // Double-check the trade result using multiple methods
         bool is_profitable = false;
         bool is_loss = false;

         // Method 1: Check deal profit directly
         if(deal_profit > 0)
         {
            is_profitable = true;
            Print("Method 1 (deal_profit): Trade is PROFITABLE with profit ", DoubleToString(deal_profit, 2));
         }
         else
         {
            is_loss = true;
            Print("Method 1 (deal_profit): Trade is a LOSS with loss ", DoubleToString(MathAbs(deal_profit), 2));
         }

         // Method 2: Check deal reason
         if(deal_reason == DEAL_REASON_TP)
         {
            // If take profit was hit, it should be profitable
            if(!is_profitable)
            {
               Print("WARNING: Deal reason is TP but profit is not positive! Possible discrepancy.");
               // Trust the deal_profit value more than the reason
               is_profitable = false;
               is_loss = true;
            }
         }
         else if(deal_reason == DEAL_REASON_SL)
         {
            // If stop loss was hit, it should be a loss
            if(!is_loss)
            {
               Print("WARNING: Deal reason is SL but profit is positive! Possible discrepancy.");
               // Trust the deal_profit value more than the reason
               is_profitable = true;
               is_loss = false;
            }
         }

         // Method 3: Additional verification with order info
         ulong order_ticket = HistoryDealGetInteger(deal_ticket, DEAL_ORDER);
         if(order_ticket > 0 && HistoryOrderSelect(order_ticket))
         {
            ENUM_ORDER_TYPE order_type = (ENUM_ORDER_TYPE)HistoryOrderGetInteger(order_ticket, ORDER_TYPE);

            Print("Method 3 (order): Order #", order_ticket,
                  ", Type: ", EnumToString(order_type));

            // Additional verification can be done here if needed
         }

         // Log detailed information for debugging
         Print("DETAILED TRADE INFO - Position: #", position_ticket,
               ", Deal: #", deal_ticket,
               ", Profit: ", DoubleToString(deal_profit, 2),
               ", Reason: ", EnumToString(deal_reason),
               ", Is Profitable: ", is_profitable,
               ", Is Loss: ", is_loss);

         // Update martingale variables based on verified trade result
         // Use a time-based guard to prevent multiple updates for the same trade
         if(TimeCurrent() - g_last_martingale_update > 1) // At least 1 second between updates
         {
            g_last_martingale_update = TimeCurrent();

            // Enhanced logging for martingale debugging
            Print("MARTINGALE DEBUG - Processing trade: Position #", position_ticket,
                  ", Deal #", deal_ticket,
                  ", Was Buy: ", was_buy ? "Yes" : "No",
                  ", Profit: ", DoubleToString(deal_profit, 2),
                  ", Is Profitable: ", is_profitable ? "Yes" : "No",
                  ", Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
                  ", Consecutive Losses: ", g_consecutive_losses,
                  ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2));

            // IMPORTANT CHANGE: Toggle the next trade direction regardless of profit/loss
            // This is the key change for the alternating buy/sell sequence
            g_next_trade_is_buy = !was_buy; // Next trade will be opposite of the last one

            // Log the next trade direction
            Print("ALTERNATING TRADE DIRECTION - Last trade was ", (was_buy ? "BUY" : "SELL"),
                  ", Next trade will be ", (g_next_trade_is_buy ? "BUY" : "SELL"));

            if(is_profitable) // Profitable trade
            {
               // Update martingale progression on profitable trade
               if(Use_Martingale)
               {
                  // Calculate actual profit after accounting for spread cost
                  double actual_profit = deal_profit;

                  // Subtract spread cost from profit if enabled
                  if(Include_Spread_In_Martingale)
                  {
                     double spread_cost = CalculateSpreadCost(g_current_lot_size);
                     actual_profit -= spread_cost;
                     Print("Including spread cost in profit calculation: ", DoubleToString(spread_cost, 2),
                           ", Actual profit after spread: ", DoubleToString(actual_profit, 2));
                  }

                  // Subtract profit from total loss amount
                  g_total_loss_amount -= actual_profit;

                  // If we've recovered all losses or made a profit, reset martingale
                  if(g_total_loss_amount <= 0)
                  {
                     g_consecutive_losses = 0;
                     g_current_lot_size = Fixed_Lot_Size; // Reset to initial lot size
                     g_total_loss_amount = 0; // Reset loss counter
                     Print("All losses recovered. Martingale reset. Next trade lot size: ", DoubleToString(g_current_lot_size, 2));
                  }
                  else
                  {
                     Print("Profitable trade, but still recovering losses. Remaining loss to recover: ", DoubleToString(g_total_loss_amount, 2));
                  }
               }

               if(was_tp_hit)
               {
                  Print("Take profit was hit on position #", position_ticket, " with profit ", DoubleToString(deal_profit, 2));

                  // MODIFIED BEHAVIOR: Always set the flag to wait for a proper crossover before opening a new trade
                  g_waiting_for_crossover = true;
                  Print("Crossover waiting flag set to true after TP hit. Will wait for a proper EMA crossover before opening new trades.");

                  // IMPORTANT FIX: Always reset martingale variables when take profit is hit
                  // This ensures we start fresh with the initial lot size after a winning trade
                  if(Use_Martingale)
                  {
                     // Store old values for logging
                     double old_lot_size = g_current_lot_size;
                     int old_consecutive_losses = g_consecutive_losses;
                     double old_total_loss = g_total_loss_amount;

                     // Reset martingale variables
                     g_consecutive_losses = 0;
                     g_current_lot_size = Fixed_Lot_Size;
                     g_total_loss_amount = 0;

                     Print("MARTINGALE RESET AFTER TP - Old lot size: ", DoubleToString(old_lot_size, 2),
                           ", New lot size: ", DoubleToString(g_current_lot_size, 2),
                           ", Old consecutive losses: ", old_consecutive_losses,
                           ", Old total loss: ", DoubleToString(old_total_loss, 2));
                  }
               }
               else
               {
                  // Position closed with profit but not by TP (manual close or other reason)
                  Print("Position closed with profit on ", g_symbol, " but not by take profit. Reason: ", EnumToString(deal_reason));

                  // MODIFIED BEHAVIOR: Always set the flag to wait for a proper crossover before opening a new trade
                  g_waiting_for_crossover = true;
                  Print("Crossover waiting flag set to true after manual close with profit. Will wait for a proper EMA crossover before opening new trades.");
               }
            }
            else // Losing trade
            {
               if(Use_Martingale)
               {
                  // Calculate actual loss including spread cost if enabled
                  double actual_loss = MathAbs(deal_profit);

                  // Add spread cost to loss calculation if enabled
                  if(Include_Spread_In_Martingale)
                  {
                     double spread_cost = CalculateSpreadCost(g_current_lot_size);
                     actual_loss += spread_cost;
                     Print("Including spread cost in martingale calculation: ", DoubleToString(spread_cost, 2),
                           ", Total loss with spread: ", DoubleToString(actual_loss, 2));
                  }

                  // Add loss to total loss amount
                  g_total_loss_amount += actual_loss;
                  g_consecutive_losses++;

                  // Calculate new lot size for martingale progression
                  if(g_consecutive_losses <= Max_Martingale_Level)
                  {
                     // Get lot step from broker
                     double lotStep = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_STEP);
                     int lotDigits = 0;

                     // Calculate decimal places for lot normalization
                     if(lotStep != 0)
                        lotDigits = (int)MathAbs(MathLog10(lotStep));

                     // Calculate required lot size to recover losses
                     double required_profit = g_total_loss_amount;

                     // Calculate new lot size based on required profit
                     double new_lot_size;

                     // If we're including spread in calculations, adjust the lot size to account for spread cost
                     if(Include_Spread_In_Martingale)
                     {
                        // Calculate potential spread cost for next trade
                        double potential_spread_cost = CalculateSpreadCost(g_current_lot_size * Martingale_Multiplier);

                        // Adjust required profit to include potential spread cost
                        required_profit += potential_spread_cost;

                        // Calculate lot size needed to recover all losses including spread costs
                        new_lot_size = NormalizeDouble(g_current_lot_size * Martingale_Multiplier, lotDigits);

                        Print("Martingale calculation with spread - Required profit: ", DoubleToString(required_profit, 2),
                              ", Potential spread cost: ", DoubleToString(potential_spread_cost, 2));
                     }
                     else
                     {
                        // Standard martingale calculation without spread consideration
                        new_lot_size = NormalizeDouble(g_current_lot_size * Martingale_Multiplier, lotDigits);
                     }

                     // Ensure lot size doesn't exceed maximum
                     if(new_lot_size > Max_Lot_Size)
                        new_lot_size = Max_Lot_Size;

                     // Store the old lot size for logging
                     double old_lot_size = g_current_lot_size;

                     // Update the current lot size
                     g_current_lot_size = new_lot_size;

                     Print("MARTINGALE UPDATE - Loss detected: ", DoubleToString(actual_loss, 2),
                           ", Total loss to recover: ", DoubleToString(g_total_loss_amount, 2),
                           ", Martingale level: ", g_consecutive_losses, "/", Max_Martingale_Level,
                           ", Previous lot size: ", DoubleToString(old_lot_size, lotDigits),
                           ", Next trade lot size increased to: ", DoubleToString(g_current_lot_size, lotDigits));
                  }
                  else
                  {
                     Print("Maximum martingale level reached (", Max_Martingale_Level, "). Resetting to initial lot size.");
                     g_consecutive_losses = 0;
                     g_current_lot_size = Fixed_Lot_Size;
                     // We don't reset g_total_loss_amount here to keep track of overall losses
                  }
               }

               if(was_sl_hit)
               {
                  // Log when stop loss is hit
                  Print("Stop loss hit on ", g_symbol, ". Will wait for EMA crossover signal to open new trade.");

                  // MODIFIED BEHAVIOR: Always set the flag to wait for a proper crossover before opening a new trade
                  g_waiting_for_crossover = true;
                  Print("Crossover waiting flag set to true after SL hit. Will wait for a proper EMA crossover before opening new trades.");
                  Print("IMPORTANT: Martingale progression will continue with next trade. Current lot size: ",
                        DoubleToString(g_current_lot_size, 2));

                  // Note: We don't reset martingale variables here because we want to continue the martingale progression
                  // after a stop loss. The lot size will be increased according to the martingale rules.
               }
               else
               {
                  // Position closed with loss but not by SL (manual close or other reason)
                  Print("Position closed with loss on ", g_symbol, ". Reason: ", EnumToString(deal_reason),
                        ". Martingale will increase lot size to: ", DoubleToString(g_current_lot_size, 2));

                  // MODIFIED BEHAVIOR: Always set the flag to wait for a proper crossover before opening a new trade
                  g_waiting_for_crossover = true;
                  Print("Crossover waiting flag set to true after manual close with loss. Will wait for a proper EMA crossover before opening new trades.");
               }
            }

            // Final verification of martingale state after processing
            Print("MARTINGALE STATE AFTER PROCESSING - Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
                  ", Consecutive Losses: ", g_consecutive_losses,
                  ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2));
         }
         else
         {
            Print("Skipping martingale update - too soon after previous update.");
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check if a position ID is in the processed array                  |
//+------------------------------------------------------------------+
bool IsPositionInArray(ulong position_id)
{
   // Check if position ID is in array
   for(int i = 0; i < g_last_closed_position_count; i++)
   {
      if(g_last_closed_positions[i] == position_id)
      {
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if a ticket is in the processed array                       |
//+------------------------------------------------------------------+
bool IsTicketInArray(ulong ticket)
{
   // Check if ticket is in array
   for(int i = 0; i < g_last_closed_count; i++)
   {
      if(g_last_closed_tickets[i] == ticket)
      {
         return true;
      }
   }

   // Also check in the position ID array for extra safety
   for(int i = 0; i < g_last_closed_position_count; i++)
   {
      if(g_last_closed_positions[i] == ticket)
      {
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if a deal ticket is in the processed array                  |
//+------------------------------------------------------------------+
bool IsDealInArray(ulong deal_ticket)
{
   // Check if deal ticket is in array
   for(int i = 0; i < g_last_closed_position_count; i++)
   {
      if(g_last_closed_deals[i] == deal_ticket)
      {
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Add a position and deal to the processed arrays                   |
//+------------------------------------------------------------------+
void AddPositionAndDealToArrays(ulong position_id, ulong deal_ticket)
{
   // First check if the position is already in the array to avoid duplicates
   if(IsPositionInArray(position_id))
   {
      // Position already exists in array, no need to add it again
      return;
   }

   // Reset the arrays if they're full
   if(g_last_closed_position_count >= ArraySize(g_last_closed_positions))
   {
      Print("Position/Deal arrays full (size: ", ArraySize(g_last_closed_positions), "). Clearing arrays.");
      ArrayInitialize(g_last_closed_positions, 0); // Clear the position array
      ArrayInitialize(g_last_closed_deals, 0);     // Clear the deal array
      g_last_closed_position_count = 0;
   }

   // Add the position and deal to the arrays
   g_last_closed_positions[g_last_closed_position_count] = position_id;
   g_last_closed_deals[g_last_closed_position_count] = deal_ticket;
   g_last_closed_position_count++;

   Print("Added position #", position_id, " and deal #", deal_ticket, " to tracking arrays. Current count: ", g_last_closed_position_count);
}

//+------------------------------------------------------------------+
//| Add a ticket to the processed array                               |
//+------------------------------------------------------------------+
void AddTicketToArray(ulong ticket)
{
   // First check if the ticket is already in the array to avoid duplicates
   if(IsTicketInArray(ticket))
   {
      // Ticket already exists in array, no need to add it again
      return;
   }

   // Reset the array if it's full
   if(g_last_closed_count >= ArraySize(g_last_closed_tickets))
   {
      Print("Ticket array full (size: ", ArraySize(g_last_closed_tickets), "). Clearing array.");
      ArrayInitialize(g_last_closed_tickets, 0); // Clear the array
      g_last_closed_count = 0;
   }

   // Add the ticket to the array
   g_last_closed_tickets[g_last_closed_count] = ticket;
   g_last_closed_count++;
}

//+------------------------------------------------------------------+
//| Count open positions for all symbols                              |
//+------------------------------------------------------------------+
int CountOpenPositions()
{
   int count = 0;

   // Loop through all open positions
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket))
      {
         // Check if the position belongs to our EA
         if(PositionGetInteger(POSITION_MAGIC) == Magic_Number)
         {
            count++;
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Count positions for the current symbol                            |
//+------------------------------------------------------------------+
int CountSymbolPositions()
{
   int count = 0;

   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket))
      {
         if(PositionGetString(POSITION_SYMBOL) == g_symbol &&
            PositionGetInteger(POSITION_MAGIC) == Magic_Number)
         {
            count++;
         }
      }
   }

   return count;
}

//+------------------------------------------------------------------+
//| Check if we have a position of the specified type                 |
//+------------------------------------------------------------------+
bool HasPositionType(ENUM_POSITION_TYPE posType)
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket))
      {
         if(PositionGetString(POSITION_SYMBOL) == g_symbol &&
            PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
            PositionGetInteger(POSITION_TYPE) == posType)
         {
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check for entry signals on the current symbol                      |
//+------------------------------------------------------------------+
void CheckEntrySignal()
{
   // Skip if we're still in initialization
   if(!g_initialization_complete)
      return;

   // Check if enough time has passed since the last trade
   if(TimeCurrent() - g_last_trade_time < Min_Trade_Interval)
      return;

   // Get the EMA values for the last two completed candles
   double ema_prev = GetEMA(1);      // Last completed candle
   double ema_prev_prev = GetEMA(2); // Second-to-last completed candle

   // Get the close prices for the last two completed candles
   double close_prev = GetClose(1);      // Last completed candle
   double close_prev_prev = GetClose(2); // Second-to-last completed candle

   // Skip if we couldn't get valid values
   if(ema_prev < 0 || ema_prev_prev < 0 || close_prev < 0 || close_prev_prev < 0)
      return;

   // If we're waiting for a crossover after TP/SL was hit, check if we have a valid crossover
   if(g_waiting_for_crossover)
   {
      // Check if we have a valid crossover (either direction)
      bool crossover_occurred = (close_prev_prev < ema_prev_prev && close_prev > ema_prev) ||
                               (close_prev_prev > ema_prev_prev && close_prev < ema_prev);

      if(crossover_occurred)
      {
         // Reset the waiting flag since we've detected a proper crossover
         g_waiting_for_crossover = false;
         Print("Detected proper EMA crossover after TP/SL. Crossover waiting flag reset to false.");

         // Log current martingale state before any potential reset
         if(Use_Martingale)
         {
            Print("MARTINGALE STATE BEFORE CROSSOVER RESET - Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
                  ", Consecutive Losses: ", g_consecutive_losses,
                  ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2));
         }

         // Log current martingale state after crossover
         if(Use_Martingale)
         {
            Print("MARTINGALE STATE AFTER CROSSOVER - Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
                  ", Consecutive Losses: ", g_consecutive_losses,
                  ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2));

            // Verify that martingale state is consistent with our expectations
            // After TP, we should have already reset the martingale variables in the CheckClosedPositions function
            // After SL, we should maintain the martingale progression

            // Double-check that after TP, the lot size is reset to initial value
            if(g_total_loss_amount <= 0.01 && g_current_lot_size != Fixed_Lot_Size)
            {
               Print("INCONSISTENCY DETECTED: After TP, lot size should be reset to initial value.");
               Print("Forcing reset of martingale variables to ensure consistency.");
               g_current_lot_size = Fixed_Lot_Size;
               g_consecutive_losses = 0;
               g_total_loss_amount = 0;
            }

            // After crossover is detected, we're ready for a new trade cycle
            Print("Crossover detected. Ready for new trade cycle with lot size: ", DoubleToString(g_current_lot_size, 2));
         }

         // Now that we've detected a crossover, we can open a trade based on the alternating sequence
         if(g_next_trade_is_buy)
         {
            Print("ALTERNATING SEQUENCE: Opening BUY trade after EMA crossover");
            ExecuteBuyTrade();
         }
         else
         {
            Print("ALTERNATING SEQUENCE: Opening SELL trade after EMA crossover");
            ExecuteSellTrade();
         }
      }
      else
      {
         // No crossover yet, so don't open any trades
         return;
      }
   }
   else
   {
      // We're not waiting for a crossover after TP/SL, so check for normal entry signals

      // EMA and close price values are already retrieved above

      // Debug output - print values every 10 candles
      static int debug_counter = 0;
      debug_counter++;
      if(debug_counter >= 10)
      {
         debug_counter = 0;
         Print("DEBUG - Symbol: ", g_symbol,
               ", Last Closed Candle Close: ", DoubleToString(close_prev, 5),
               ", Last Closed Candle EMA: ", DoubleToString(ema_prev, 5),
               ", Previous Closed Candle Close: ", DoubleToString(close_prev_prev, 5),
               ", Previous Closed Candle EMA: ", DoubleToString(ema_prev_prev, 5),
               ", Position: ", (close_prev > ema_prev ? "Above EMA" : "Below EMA"),
               ", Next Trade: ", (g_next_trade_is_buy ? "BUY" : "SELL"));
      }

      // Check if we have a valid crossover (either direction)
      bool crossover_occurred = (close_prev_prev < ema_prev_prev && close_prev > ema_prev) ||
                               (close_prev_prev > ema_prev_prev && close_prev < ema_prev);

      // If we have a crossover, open a trade based on the alternating sequence
      if(crossover_occurred)
      {
         Print("EMA CROSSOVER DETECTED - Previous Candle Close: ", DoubleToString(close_prev_prev, 5),
               ", Previous Candle EMA: ", DoubleToString(ema_prev_prev, 5),
               ", Last Closed Candle Close: ", DoubleToString(close_prev, 5),
               ", Last Closed Candle EMA: ", DoubleToString(ema_prev, 5));

         // Open trade based on the alternating sequence
         if(g_next_trade_is_buy)
         {
            Print("ALTERNATING SEQUENCE: Opening BUY trade after EMA crossover");
            ExecuteBuyTrade();
         }
         else
         {
            Print("ALTERNATING SEQUENCE: Opening SELL trade after EMA crossover");
            ExecuteSellTrade();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get EMA value for current symbol and shift                        |
//+------------------------------------------------------------------+
double GetEMA(int shift)
{
   // Use the global EMA handle that was created in OnInit
   if(g_ema_handle == INVALID_HANDLE)
   {
      Print("EMA indicator handle is invalid. Trying to recreate.");
      g_ema_handle = iMA(g_symbol, PERIOD_M1, EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
      if(g_ema_handle == INVALID_HANDLE)
      {
         Print("Failed to recreate EMA indicator handle. Error: ", GetLastError());
         return -1;
      }
   }

   // Get the EMA value
   double buffer[];
   if(CopyBuffer(g_ema_handle, 0, shift, 1, buffer) <= 0)
   {
      Print("Failed to copy EMA buffer for ", g_symbol, ". Error: ", GetLastError());
      return -1;
   }

   return buffer[0];
}

//+------------------------------------------------------------------+
//| Get close price for current symbol and shift                      |
//+------------------------------------------------------------------+
double GetClose(int shift)
{
   double close_array[];
   if(CopyClose(g_symbol, PERIOD_M1, shift, 1, close_array) <= 0)
   {
      Print("Failed to copy close price for ", g_symbol, ". Error: ", GetLastError());
      return -1;
   }

   return close_array[0];
}

//+------------------------------------------------------------------+
//| Execute a buy trade                                               |
//+------------------------------------------------------------------+
void ExecuteBuyTrade()
{
   // Check if we have any open positions
   if(CountOpenPositions() > 0)
   {
      Print("Already have an open position. Skipping new BUY trade.");
      return;
   }

   // Update spread statistics before trade
   UpdateSpreadStats();

   // Enhanced spread filter
   if(!IsSpreadAcceptable())
   {
      Print("Spread conditions not acceptable for BUY trade. Current spread: ",
            DoubleToString(g_current_spread, 1), " points.");
      return;
   }

   // Free margin check
   double lotSize = Use_Martingale ? g_current_lot_size : Fixed_Lot_Size;
   double margin_required = 0.0;
   if(!OrderCalcMargin(ORDER_TYPE_BUY, g_symbol, lotSize, SymbolInfoDouble(g_symbol, SYMBOL_ASK), margin_required))
   {
      Print("Failed to calculate margin for BUY trade. Error: ", GetLastError());
      return;
   }
   if(AccountInfoDouble(ACCOUNT_FREEMARGIN) < margin_required)
   {
      Print("Not enough free margin for BUY trade. Required: ", margin_required, ", Available: ", AccountInfoDouble(ACCOUNT_FREEMARGIN));
      return;
   }

   Print("Attempting to open BUY trade on ", g_symbol);

   // Get current price
   double ask = SymbolInfoDouble(g_symbol, SYMBOL_ASK);
   if(ask == 0) return;

   Print("TRADE EXECUTION - Using lot size: ", DoubleToString(lotSize, 2), " for BUY trade",
         (Use_Martingale ? ", Martingale level: " + IntegerToString(g_consecutive_losses) +
         ", Total loss to recover: " + DoubleToString(g_total_loss_amount, 2) : ""));

   // Enhanced martingale logging
   if(Use_Martingale)
   {
      Print("MARTINGALE STATE BEFORE BUY - Fixed Lot Size: ", DoubleToString(Fixed_Lot_Size, 2),
            ", Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
            ", Consecutive Losses: ", g_consecutive_losses,
            ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2));
   }

   // Calculate spread-adjusted stop loss and take profit levels
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);

   // Use spread-adjusted TP/SL calculations
   double stopLossPrice = CalculateSpreadAdjustedBuyStopLoss(ask);
   double takeProfitPrice = CalculateSpreadAdjustedBuyTakeProfit(ask);

   // Log the spread adjustment
   if(Adjust_For_Spread)
   {
      double spread_adjustment = g_current_spread * point * Spread_Buffer;
      Print("Spread adjustment applied to BUY order - Current spread: ", DoubleToString(g_current_spread, 1),
            ", Adjustment: ", DoubleToString(spread_adjustment / point, 1), " points");
   }

   // Check if stop loss is valid (not too close to current price)
   double minStopLevel = SymbolInfoInteger(g_symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   if(ask - stopLossPrice < minStopLevel)
   {
      Print("Stop loss too close to current price on ", g_symbol, ". Minimum distance: ", minStopLevel, " points.");
      return;
   }
   // Check if take profit is valid (not too close to current price)
   if(takeProfitPrice - ask < minStopLevel)
   {
      Print("Take profit too close to current price on ", g_symbol, ". Minimum distance: ", minStopLevel, " points.");
      return;
   }

   // Execute buy order
   if(trade.Buy(lotSize, g_symbol, ask, stopLossPrice, takeProfitPrice, "EMA Alternating Strategy"))
   {
      g_last_trade_time = TimeCurrent();
      ulong order_ticket = trade.ResultOrder();
      ulong deal_ticket = trade.ResultDeal();
      ulong position_id = trade.ResultOrder(); // In MT5, position ID is the same as order ticket

      // Increment the trade count
      g_trade_count++;

      // Toggle the next trade direction
      g_next_trade_is_buy = false; // Next trade will be SELL

      Print("Buy order executed successfully on ", g_symbol,
            ". Order Ticket: ", order_ticket,
            ", Position ID: ", position_id,
            ", Deal Ticket: ", deal_ticket,
            ", Lot Size: ", DoubleToString(lotSize, 2),
            ", Trade #", g_trade_count,
            ", Next trade will be SELL");
   }
   else
   {
      Print("Failed to execute buy order on ", g_symbol, ". Error: ", GetLastError(), " - ", ErrorDescription(GetLastError()));
   }
}

//+------------------------------------------------------------------+
//| Execute a sell trade                                              |
//+------------------------------------------------------------------+
void ExecuteSellTrade()
{
   // Check if we have any open positions
   if(CountOpenPositions() > 0)
   {
      Print("Already have an open position. Skipping new SELL trade.");
      return;
   }

   // Update spread statistics before trade
   UpdateSpreadStats();

   // Enhanced spread filter
   if(!IsSpreadAcceptable())
   {
      Print("Spread conditions not acceptable for SELL trade. Current spread: ",
            DoubleToString(g_current_spread, 1), " points.");
      return;
   }

   // Free margin check
   double lotSize = Use_Martingale ? g_current_lot_size : Fixed_Lot_Size;
   double margin_required = 0.0;
   if(!OrderCalcMargin(ORDER_TYPE_SELL, g_symbol, lotSize, SymbolInfoDouble(g_symbol, SYMBOL_BID), margin_required))
   {
      Print("Failed to calculate margin for SELL trade. Error: ", GetLastError());
      return;
   }
   if(AccountInfoDouble(ACCOUNT_FREEMARGIN) < margin_required)
   {
      Print("Not enough free margin for SELL trade. Required: ", margin_required, ", Available: ", AccountInfoDouble(ACCOUNT_FREEMARGIN));
      return;
   }

   Print("Attempting to open SELL trade on ", g_symbol);

   // Get current price
   double bid = SymbolInfoDouble(g_symbol, SYMBOL_BID);
   if(bid == 0) return;

   Print("TRADE EXECUTION - Using lot size: ", DoubleToString(lotSize, 2), " for SELL trade",
         (Use_Martingale ? ", Martingale level: " + IntegerToString(g_consecutive_losses) +
         ", Total loss to recover: " + DoubleToString(g_total_loss_amount, 2) : ""));

   // Enhanced martingale logging
   if(Use_Martingale)
   {
      Print("MARTINGALE STATE BEFORE SELL - Fixed Lot Size: ", DoubleToString(Fixed_Lot_Size, 2),
            ", Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
            ", Consecutive Losses: ", g_consecutive_losses,
            ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2));
   }

   // Calculate spread-adjusted stop loss and take profit levels
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);

   // Use spread-adjusted TP/SL calculations
   double stopLossPrice = CalculateSpreadAdjustedSellStopLoss(bid);
   double takeProfitPrice = CalculateSpreadAdjustedSellTakeProfit(bid);

   // Log the spread adjustment
   if(Adjust_For_Spread)
   {
      double spread_adjustment = g_current_spread * point * Spread_Buffer;
      Print("Spread adjustment applied to SELL order - Current spread: ", DoubleToString(g_current_spread, 1),
            ", Adjustment: ", DoubleToString(spread_adjustment / point, 1), " points");
   }

   // Check if stop loss is valid (not too close to current price)
   double minStopLevel = SymbolInfoInteger(g_symbol, SYMBOL_TRADE_STOPS_LEVEL) * SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   if(stopLossPrice - bid < minStopLevel)
   {
      Print("Stop loss too close to current price on ", g_symbol, ". Minimum distance: ", minStopLevel, " points.");
      return;
   }
   // Check if take profit is valid (not too close to current price)
   if(bid - takeProfitPrice < minStopLevel)
   {
      Print("Take profit too close to current price on ", g_symbol, ". Minimum distance: ", minStopLevel, " points.");
      return;
   }

   // Execute sell order
   if(trade.Sell(lotSize, g_symbol, bid, stopLossPrice, takeProfitPrice, "EMA Alternating Strategy"))
   {
      g_last_trade_time = TimeCurrent();
      ulong order_ticket = trade.ResultOrder();
      ulong deal_ticket = trade.ResultDeal();
      ulong position_id = trade.ResultOrder(); // In MT5, position ID is the same as order ticket

      // Increment the trade count
      g_trade_count++;

      // Toggle the next trade direction
      g_next_trade_is_buy = true; // Next trade will be BUY

      Print("Sell order executed successfully on ", g_symbol,
            ". Order Ticket: ", order_ticket,
            ", Position ID: ", position_id,
            ", Deal Ticket: ", deal_ticket,
            ", Lot Size: ", DoubleToString(lotSize, 2),
            ", Trade #", g_trade_count,
            ", Next trade will be BUY");
   }
   else
   {
      Print("Failed to execute sell order on ", g_symbol, ". Error: ", GetLastError(), " - ", ErrorDescription(GetLastError()));
   }
}

//+------------------------------------------------------------------+
//| Validate input parameters                                         |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   // Check Fixed_Lot_Size and Max_Lot_Size
   double minLot = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_STEP);

   if(Fixed_Lot_Size < minLot || Fixed_Lot_Size > maxLot)
   {
      Print("Invalid Fixed_Lot_Size: ", Fixed_Lot_Size, ". Must be between ", minLot, " and ", maxLot);
      return false;
   }

   if(Max_Lot_Size < minLot || Max_Lot_Size > maxLot)
   {
      Print("Invalid Max_Lot_Size: ", Max_Lot_Size, ". Must be between ", minLot, " and ", maxLot);
      return false;
   }

   // Check if lot sizes are multiples of lot step
   if(MathAbs(Fixed_Lot_Size - MathRound(Fixed_Lot_Size / lotStep) * lotStep) > 0.0000001)
   {
      Print("Invalid Fixed_Lot_Size: ", Fixed_Lot_Size, ". Must be a multiple of ", lotStep);
      return false;
   }

   if(MathAbs(Max_Lot_Size - MathRound(Max_Lot_Size / lotStep) * lotStep) > 0.0000001)
   {
      Print("Invalid Max_Lot_Size: ", Max_Lot_Size, ". Must be a multiple of ", lotStep);
      return false;
   }

   // Check EMA_Period
   if(EMA_Period <= 0)
   {
      Print("Invalid EMA_Period: ", EMA_Period, ". Must be greater than 0.");
      return false;
   }

   // Check TP_Pips and SL_Pips
   if(TP_Pips <= 0)
   {
      Print("Invalid TP_Pips: ", TP_Pips, ". Must be greater than 0.");
      return false;
   }

   if(SL_Pips <= 0)
   {
      Print("Invalid SL_Pips: ", SL_Pips, ". Must be greater than 0.");
      return false;
   }

   // Check Initialization_Delay
   if(Initialization_Delay < 0)
   {
      Print("Invalid Initialization_Delay: ", Initialization_Delay, ". Must be greater than or equal to 0.");
      return false;
   }

   // Check Min_Trade_Interval
   if(Min_Trade_Interval < 0)
   {
      Print("Invalid Min_Trade_Interval: ", Min_Trade_Interval, ". Must be greater than or equal to 0.");
      return false;
   }

   // No need to check Max_Concurrent_Trades as we only allow one position at a time

   // Check spread handling parameters
   if(Max_Allowed_Spread <= 0)
   {
      Print("Invalid Max_Allowed_Spread: ", Max_Allowed_Spread, ". Must be greater than 0.");
      return false;
   }

   if(Spread_Buffer <= 0)
   {
      Print("Invalid Spread_Buffer: ", Spread_Buffer, ". Must be greater than 0.");
      return false;
   }

   if(Spread_History_Length <= 0)
   {
      Print("Invalid Spread_History_Length: ", Spread_History_Length, ". Must be greater than 0.");
      return false;
   }

   if(Max_Spread_Deviation <= 0)
   {
      Print("Invalid Max_Spread_Deviation: ", Max_Spread_Deviation, ". Must be greater than 0.");
      return false;
   }

   // Check Martingale parameters
   if(Use_Martingale)
   {
      if(Martingale_Multiplier <= 1.0)
      {
         Print("Invalid Martingale_Multiplier: ", Martingale_Multiplier, ". Must be greater than 1.0.");
         return false;
      }

      if(Max_Martingale_Level <= 0)
      {
         Print("Invalid Max_Martingale_Level: ", Max_Martingale_Level, ". Must be greater than 0.");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Get error description                                             |
//+------------------------------------------------------------------+
string ErrorDescription(int error_code)
{
   string error_string;

   switch(error_code)
   {
      case 0:   error_string = "No error"; break;
      case 4051: error_string = "Invalid function parameter value"; break;
      case 4062: error_string = "Trading disabled"; break;
      case 4063: error_string = "Not enough money"; break;
      case 4073: error_string = "Invalid volume"; break;
      case 4074: error_string = "No quotes to process request"; break;
      case 4077: error_string = "Invalid price"; break;
      case 4099: error_string = "Pending order placed"; break;
      case 4107: error_string = "Invalid price"; break;
      case 4109: error_string = "Invalid SL or TP"; break;
      case 4110: error_string = "Autotrading disabled"; break;
      case 4111: error_string = "Longs not allowed"; break;
      case 4112: error_string = "Shorts not allowed"; break;
      case 4200: error_string = "Order already exists"; break;
      case 4203: error_string = "Max orders reached"; break;
      default:   error_string = "Unknown error"; break;
   }

   return error_string;
}

//+------------------------------------------------------------------+
//| Get the pip value for the current symbol                          |
//+------------------------------------------------------------------+
double GetPipValue()
{
   // Use input pip value for flexibility
   return Pip_Value;
}

//+------------------------------------------------------------------+
//| Check if the symbol is a gold instrument                          |
//+------------------------------------------------------------------+
bool IsGoldSymbol(string symbol)
{
   // Convert to uppercase for case-insensitive comparison
   string upperSymbol = symbol;
   StringToUpper(upperSymbol);

   // Check for common gold symbol names
   if(StringFind(upperSymbol, "XAU") >= 0 ||
      StringFind(upperSymbol, "GOLD") >= 0 ||
      StringFind(upperSymbol, "XAUUSD") >= 0)
   {
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Get current spread in points                                      |
//+------------------------------------------------------------------+
double GetCurrentSpread()
{
   double ask = SymbolInfoDouble(g_symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(g_symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);

   if(point == 0) return 0; // Avoid division by zero

   return (ask - bid) / point;
}

//+------------------------------------------------------------------+
//| Update spread statistics                                          |
//+------------------------------------------------------------------+
void UpdateSpreadStats()
{
   // Get current spread
   double current_spread = GetCurrentSpread();
   g_current_spread = current_spread;

   // Skip if spread is zero (likely invalid data)
   if(current_spread <= 0) return;

   // Initialize min spread on first valid measurement
   if(g_min_spread == 0 || current_spread < g_min_spread)
      g_min_spread = current_spread;

   // Update max spread
   if(current_spread > g_max_spread)
      g_max_spread = current_spread;

   // Update spread history array (shift values)
   for(int i = ArraySize(g_spread_history) - 1; i > 0; i--)
   {
      g_spread_history[i] = g_spread_history[i-1];
   }
   g_spread_history[0] = current_spread;

   // Update spread sum and count for average calculation
   g_spread_sum += current_spread;
   g_spread_count++;

   // Calculate average spread (limit to last Spread_History_Length measurements)
   if(g_spread_count > Spread_History_Length)
   {
      // Recalculate from history array to maintain accuracy
      g_spread_sum = 0;
      int count = MathMin(ArraySize(g_spread_history), Spread_History_Length);

      for(int i = 0; i < count; i++)
      {
         g_spread_sum += g_spread_history[i];
      }

      g_avg_spread = g_spread_sum / count;
   }
   else
   {
      g_avg_spread = g_spread_sum / g_spread_count;
   }

   // Update last spread update time
   g_last_spread_update = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Check if current spread is acceptable                             |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable()
{
   // Get current spread
   double current_spread = g_current_spread;

   // Basic check against maximum allowed spread
   if(current_spread > Max_Allowed_Spread)
   {
      Print("Spread too high: ", DoubleToString(current_spread, 1), " points. Maximum allowed: ",
            DoubleToString(Max_Allowed_Spread, 1), " points.");
      return false;
   }

   // Dynamic spread check if enabled
   if(Use_Dynamic_Spread_Filter && g_spread_count >= 10) // Need at least 10 measurements
   {
      // Check if current spread deviates too much from average
      if(current_spread > g_avg_spread * Max_Spread_Deviation)
      {
         Print("Spread deviation too high: Current ", DoubleToString(current_spread, 1),
               " vs Average ", DoubleToString(g_avg_spread, 1),
               ". Maximum allowed deviation: ", DoubleToString(Max_Spread_Deviation, 1), "x");
         return false;
      }
   }

   return true;
}

//+------------------------------------------------------------------+
//| Calculate spread-adjusted stop loss for buy order                 |
//+------------------------------------------------------------------+
double CalculateSpreadAdjustedBuyStopLoss(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();
   double spread_adjustment = 0;

   // Calculate spread adjustment if enabled
   if(Adjust_For_Spread)
   {
      spread_adjustment = g_current_spread * point * Spread_Buffer;
   }

   // Calculate stop loss with spread adjustment
   double stopLossPrice = NormalizeDouble(entry_price - (SL_Pips * pip_value) - spread_adjustment, digits);

   return stopLossPrice;
}

//+------------------------------------------------------------------+
//| Calculate spread-adjusted take profit for buy order               |
//+------------------------------------------------------------------+
double CalculateSpreadAdjustedBuyTakeProfit(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();
   double spread_adjustment = 0;

   // Calculate spread adjustment if enabled
   if(Adjust_For_Spread)
   {
      spread_adjustment = g_current_spread * point * Spread_Buffer;
   }

   // Calculate take profit with spread adjustment (add spread to TP distance)
   double takeProfitPrice = NormalizeDouble(entry_price + (TP_Pips * pip_value) + spread_adjustment, digits);

   return takeProfitPrice;
}

//+------------------------------------------------------------------+
//| Calculate spread-adjusted stop loss for sell order                |
//+------------------------------------------------------------------+
double CalculateSpreadAdjustedSellStopLoss(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();
   double spread_adjustment = 0;

   // Calculate spread adjustment if enabled
   if(Adjust_For_Spread)
   {
      spread_adjustment = g_current_spread * point * Spread_Buffer;
   }

   // Calculate stop loss with spread adjustment (add spread to SL distance)
   double stopLossPrice = NormalizeDouble(entry_price + (SL_Pips * pip_value) + spread_adjustment, digits);

   return stopLossPrice;
}

//+------------------------------------------------------------------+
//| Calculate spread-adjusted take profit for sell order              |
//+------------------------------------------------------------------+
double CalculateSpreadAdjustedSellTakeProfit(double entry_price)
{
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(g_symbol, SYMBOL_DIGITS);
   double pip_value = GetPipValue();
   double spread_adjustment = 0;

   // Calculate spread adjustment if enabled
   if(Adjust_For_Spread)
   {
      spread_adjustment = g_current_spread * point * Spread_Buffer;
   }

   // Calculate take profit with spread adjustment
   double takeProfitPrice = NormalizeDouble(entry_price - (TP_Pips * pip_value) - spread_adjustment, digits);

   return takeProfitPrice;
}

//+------------------------------------------------------------------+
//| Calculate spread cost for a trade                                 |
//+------------------------------------------------------------------+
double CalculateSpreadCost(double lot_size)
{
   double tick_value = SymbolInfoDouble(g_symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(g_symbol, SYMBOL_TRADE_TICK_SIZE);
   double point = SymbolInfoDouble(g_symbol, SYMBOL_POINT);

   if(tick_size == 0 || point == 0) return 0; // Avoid division by zero

   // Calculate spread cost based on current spread, lot size, and tick value
   double spread_in_ticks = (g_current_spread * point) / tick_size;
   double spread_cost = spread_in_ticks * tick_value * lot_size;

   return spread_cost;
}

//+------------------------------------------------------------------+
//| Verify martingale state consistency                               |
//+------------------------------------------------------------------+
void VerifyMartingaleState()
{
   // Skip if martingale is not enabled
   if(!Use_Martingale)
      return;

   // Only log periodically to avoid excessive logging
   static datetime last_verification = 0;
   if(TimeCurrent() - last_verification < 60) // Check once per minute
      return;

   last_verification = TimeCurrent();

   // Check for inconsistencies in martingale state
   if(g_consecutive_losses > 0 && g_current_lot_size <= Fixed_Lot_Size)
   {
      Print("MARTINGALE INCONSISTENCY DETECTED: Consecutive losses > 0 but lot size not increased!");
      Print("Current state: Consecutive Losses = ", g_consecutive_losses,
            ", Current Lot Size = ", DoubleToString(g_current_lot_size, 2),
            ", Fixed Lot Size = ", DoubleToString(Fixed_Lot_Size, 2));

      // Fix the inconsistency by recalculating the lot size
      double lotStep = SymbolInfoDouble(g_symbol, SYMBOL_VOLUME_STEP);
      int lotDigits = 0;

      // Calculate decimal places for lot normalization
      if(lotStep != 0)
         lotDigits = (int)MathAbs(MathLog10(lotStep));

      // Calculate the correct lot size based on consecutive losses
      double corrected_lot_size = Fixed_Lot_Size;
      for(int i = 0; i < g_consecutive_losses; i++)
      {
         corrected_lot_size *= Martingale_Multiplier;
      }

      // Normalize and cap the lot size
      corrected_lot_size = NormalizeDouble(corrected_lot_size, lotDigits);
      if(corrected_lot_size > Max_Lot_Size)
         corrected_lot_size = Max_Lot_Size;

      // Update the lot size
      Print("MARTINGALE CORRECTION: Updating lot size from ", DoubleToString(g_current_lot_size, 2),
            " to ", DoubleToString(corrected_lot_size, 2));
      g_current_lot_size = corrected_lot_size;
   }

   // Check for other potential inconsistencies
   if(g_consecutive_losses == 0 && g_current_lot_size > Fixed_Lot_Size)
   {
      Print("MARTINGALE INCONSISTENCY DETECTED: No consecutive losses but lot size is increased!");
      Print("Resetting lot size to initial value: ", DoubleToString(Fixed_Lot_Size, 2));
      g_current_lot_size = Fixed_Lot_Size;
   }

   if(g_consecutive_losses == 0 && g_total_loss_amount > 0.01)
   {
      Print("MARTINGALE INCONSISTENCY DETECTED: No consecutive losses but total loss amount > 0!");
      Print("Resetting total loss amount from ", DoubleToString(g_total_loss_amount, 2), " to 0");
      g_total_loss_amount = 0;
   }

   // Check if we're waiting for crossover after TP but lot size is not reset
   if(g_waiting_for_crossover && g_total_loss_amount <= 0.01 && g_current_lot_size != Fixed_Lot_Size)
   {
      Print("MARTINGALE INCONSISTENCY DETECTED: Waiting for crossover after TP but lot size not reset!");
      Print("Current state: Waiting for crossover = true, Total loss amount = ",
            DoubleToString(g_total_loss_amount, 2),
            ", Current lot size = ", DoubleToString(g_current_lot_size, 2),
            ", Fixed lot size = ", DoubleToString(Fixed_Lot_Size, 2));

      // Force reset to initial lot size
      Print("MARTINGALE CORRECTION: Resetting lot size to initial value after TP");
      g_current_lot_size = Fixed_Lot_Size;
      g_consecutive_losses = 0;
      g_total_loss_amount = 0;
   }

   // Periodically log the current martingale state for monitoring
   Print("MARTINGALE STATE VERIFICATION - Current Lot Size: ", DoubleToString(g_current_lot_size, 2),
         ", Consecutive Losses: ", g_consecutive_losses,
         ", Total Loss Amount: ", DoubleToString(g_total_loss_amount, 2),
         ", Fixed Lot Size: ", DoubleToString(Fixed_Lot_Size, 2),
         ", Waiting for crossover: ", (g_waiting_for_crossover ? "Yes" : "No"));
}
